import { Poppins } from "next/font/google";
import "./globals.css";
import TopBar from "@/components/navbar/TopBar";
import BottomFooter from "@/components/footer/BottomFooter";
import Footer from "@/components/footer/Footer";
import Wrapper from "@/components/Wrapper";
import NavbarToFilterLayout from "@/components/layout/NavbarToFilterLayout";

const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
  subsets: ["latin"],
});

export const metadata = {
  title: "Aarogya Global",
  description: "Aarogya Global world best health care",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={`${poppins.variable} antialiased`}>
        <Wrapper>
          <TopBar />
          <NavbarToFilterLayout />
          {children}
          <Footer />
        </Wrapper>
        <BottomFooter />
      </body>
    </html>
  );
}
