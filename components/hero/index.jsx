import React from "react";
import HeroInfo from "./HeroInfo";
import FilterBar from "./FilterBar";
import BannerImage from "./BannerImage";
import FeatureCards from "./FeatureCards";
import Wrapper from "@/components/Wrapper";

const HeroSection = () => {
  return (
    <Wrapper>
      <div className="flex flex-col items-center w-full">
        {/* Background image section - covers HeroInfo and FilterBar */}
        <div
          className="w-full bg-contain flex flex-col items-center"
          style={{ backgroundImage: "url(/backgroundImg.png)" }}
        >
          <HeroInfo />
          <FilterBar />
        </div>
        {/* Your own code section - no background image */}
        <BannerImage />
        <FeatureCards />
      </div>
    </Wrapper>
  );
};

export default HeroSection;
