import React from "react";
import Navbar from "@/components/navbar/Navbar";
import HeroInfo from "@/components/hero/HeroInfo";
import FilterBar from "@/components/hero/FilterBar";

const NavbarToFilterLayout = () => {
  return (
    <div
      className="w-full bg-contain bg-no-repeat bg-center"
      style={{ backgroundImage: "url(/backgroundImg.png)" }}
    >
      <Navbar />
      <HeroInfo />
      <FilterBar />
    </div>
  );
};

export default NavbarToFilterLayout;
